/**
 * Jobs Page for Candidates
 *
 * Displays all available jobs with filtering, search, and application functionality.
 * Uses live data from server and integrates with candidate hooks and store.
 */

import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Card,
  Button,
  Space,
  Typography,
  Pagination,
  Empty,
  Spin,
  Alert,
  Drawer,
  Switch,
  Tooltip,
  Badge,
  Statistic,
} from 'antd';
import {
  FilterOutlined,
  AppstoreOutlined,
  UnorderedListOutlined,
  ReloadOutlined,
  SearchOutlined,
  HeartOutlined,
  CarryOutOutlined,
} from '@ant-design/icons';

// Import candidate hooks
import { useJobs, useJobApplications, useSavedJobs } from '@/features/candidate/hooks';
import useAuth from '@/hooks/useAuth';

// Import job components
import {
  JobCard,
  JobFilters,
  JobDetailModal,
  JobApplicationModal,
  JobSkeletonLoader,
} from '@/features/candidate/components/jobs';

// Import utilities
import showToast from '@/utils/toast';
import useDeviceDetect from '@/hooks/useDeviceDetect';

const { Title, Text } = Typography;

const Jobs = () => {
  const { user, profile } = useAuth();
  const { isMobile } = useDeviceDetect();

  // Hooks
  const {
    jobs,
    loading: jobsLoading,
    error: jobsError,
    pagination,
    filters,
    fetchJobs,
    applyFilters,
    searchJobs,
    getJobById,
    handlePaginationChange,
    refetch,
    clearFilters,
  } = useJobs();

  const {
    applyToJob,
    loading: applicationLoading,
    hasAppliedToJob,
    getApplicationStatus,
  } = useJobApplications();

  const {
    saveJob,
    unsaveJob,
    toggleSaveJob,
    loading: savedJobsLoading,
    isJobSaved,
  } = useSavedJobs();

  // Local state
  const [viewMode, setViewMode] = useState('grid');
  const [filtersVisible, setFiltersVisible] = useState(false);
  const [selectedJob, setSelectedJob] = useState(null);
  const [jobDetailModalVisible, setJobDetailModalVisible] = useState(false);
  const [applicationModalVisible, setApplicationModalVisible] = useState(false);
  const [jobToApply, setJobToApply] = useState(null);

  // Stats calculation
  const stats = {
    totalJobs: pagination.total,
    appliedJobs: jobs.filter((job) => hasAppliedToJob(job.id)).length,
    savedJobs: jobs.filter((job) => isJobSaved(job.id)).length,
    newJobs: jobs.filter((job) => {
      const jobDate = new Date(job.created_at);
      const threeDaysAgo = new Date();
      threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);
      return jobDate > threeDaysAgo;
    }).length,
  };

  // Event handlers
  const handleJobClick = async (job) => {
    setSelectedJob(job);
    setJobDetailModalVisible(true);
  };

  const handleApplyClick = (job) => {
    if (hasAppliedToJob(job.id)) {
      showToast.info(`You have already applied to ${job.title}`);
      return;
    }
    setJobToApply(job);
    setApplicationModalVisible(true);
  };

  const handleSaveClick = async (job) => {
    await toggleSaveJob(job.id);
  };

  const handleApplicationSubmit = async (jobId, applicationData) => {
    const result = await applyToJob(jobId, applicationData);
    if (result.success) {
      setApplicationModalVisible(false);
      setJobToApply(null);
      // Refresh jobs to update application status
      refetch();
    }
  };

  const handleFilterChange = (filterValues) => {
    applyFilters(filterValues);
    if (isMobile) {
      setFiltersVisible(false);
    }
  };

  const handleViewModeChange = (mode) => {
    setViewMode(mode);
  };

  const handleRefresh = () => {
    refetch();
    showToast.success('Jobs refreshed');
  };

  const handleClearFilters = () => {
    clearFilters();
    showToast.info('Filters cleared');
  };

  // Loading state
  const loading = jobsLoading || applicationLoading || savedJobsLoading;

  return (
    <div className="jobs-page">
      {/* Page Header */}
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <Title
              level={2}
              className="mb-2"
            >
              <CarryOutOutlined className="mr-2" />
              Find Your Dream Job
            </Title>
            <Text
              type="secondary"
              className="text-base"
            >
              Discover opportunities that match your skills and aspirations
            </Text>
          </div>

          <Space wrap>
            <Button
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
              loading={loading}
            >
              Refresh
            </Button>
            <Button
              icon={<FilterOutlined />}
              onClick={() => setFiltersVisible(true)}
              className="sm:hidden"
            >
              Filters
            </Button>
          </Space>
        </div>

        {/* Stats Cards */}
        <Row
          gutter={[16, 16]}
          className="mt-6"
        >
          <Col
            xs={12}
            sm={6}
          >
            <Card
              size="small"
              className="text-center"
            >
              <Statistic
                title="Total Jobs"
                value={stats.totalJobs}
                prefix={<CarryOutOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col
            xs={12}
            sm={6}
          >
            <Card
              size="small"
              className="text-center"
            >
              <Statistic
                title="Applied"
                value={stats.appliedJobs}
                prefix={<SearchOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col
            xs={12}
            sm={6}
          >
            <Card
              size="small"
              className="text-center"
            >
              <Statistic
                title="Saved"
                value={stats.savedJobs}
                prefix={<HeartOutlined />}
                valueStyle={{ color: '#eb2f96' }}
              />
            </Card>
          </Col>
          <Col
            xs={12}
            sm={6}
          >
            <Card
              size="small"
              className="text-center"
            >
              <Badge
                count={stats.newJobs}
                showZero={false}
              >
                <Statistic
                  title="New Jobs"
                  value={stats.newJobs}
                  valueStyle={{ color: '#fa8c16' }}
                />
              </Badge>
            </Card>
          </Col>
        </Row>
      </div>

      {/* Error Alert */}
      {jobsError && (
        <Alert
          message="Error Loading Jobs"
          description={jobsError}
          type="error"
          showIcon
          closable
          className="mb-4"
          action={
            <Button
              size="small"
              onClick={handleRefresh}
            >
              Retry
            </Button>
          }
        />
      )}

      <Row gutter={[24, 24]}>
        {/* Filters Sidebar - Desktop */}
        {!isMobile && (
          <Col
            xs={24}
            lg={6}
          >
            <div className="sticky top-4">
              <JobFilters
                onFilter={handleFilterChange}
                initialValues={filters}
                loading={loading}
              />
              {Object.keys(filters).length > 0 && (
                <Button
                  block
                  onClick={handleClearFilters}
                  className="mt-4"
                >
                  Clear All Filters
                </Button>
              )}
            </div>
          </Col>
        )}

        {/* Jobs Content */}
        <Col
          xs={24}
          lg={isMobile ? 24 : 18}
        >
          {/* View Controls */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-4">
              <Text strong>{pagination.total} jobs found</Text>
              {Object.keys(filters).length > 0 && (
                <Badge
                  count={Object.keys(filters).length}
                  size="small"
                >
                  <Text type="secondary">Filters applied</Text>
                </Badge>
              )}
            </div>

            <Space>
              <Tooltip title="Grid View">
                <Button
                  type={viewMode === 'grid' ? 'primary' : 'default'}
                  icon={<AppstoreOutlined />}
                  onClick={() => handleViewModeChange('grid')}
                />
              </Tooltip>
              <Tooltip title="List View">
                <Button
                  type={viewMode === 'list' ? 'primary' : 'default'}
                  icon={<UnorderedListOutlined />}
                  onClick={() => handleViewModeChange('list')}
                />
              </Tooltip>
            </Space>
          </div>

          {/* Jobs Grid/List */}
          {loading ? (
            <JobSkeletonLoader
              count={6}
              viewMode={viewMode}
            />
          ) : jobs.length === 0 ? (
            <Empty
              description="No jobs found"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              className="my-8"
            >
              <Button
                type="primary"
                onClick={handleClearFilters}
              >
                Clear Filters
              </Button>
            </Empty>
          ) : (
            <>
              <div
                className={
                  viewMode === 'grid'
                    ? 'grid grid-cols-1 lg:grid-cols-2 gap-6'
                    : 'flex flex-col gap-4'
                }
              >
                {jobs.map((job) => (
                  <JobCard
                    key={job.id}
                    job={job}
                    isApplied={hasAppliedToJob(job.id)}
                    isSaved={isJobSaved(job.id)}
                    onApply={() => handleApplyClick(job)}
                    onSave={() => handleSaveClick(job)}
                    onClick={() => handleJobClick(job)}
                    viewMode={viewMode}
                    showMatchPercentage={true}
                    loading={loading}
                  />
                ))}
              </div>

              {/* Pagination */}
              {pagination.total > pagination.pageSize && (
                <div className="flex justify-center mt-8">
                  <Pagination
                    current={pagination.current}
                    total={pagination.total}
                    pageSize={pagination.pageSize}
                    onChange={handlePaginationChange}
                    showSizeChanger
                    showQuickJumper
                    showTotal={(total, range) => `${range[0]}-${range[1]} of ${total} jobs`}
                    pageSizeOptions={['12', '24', '48']}
                  />
                </div>
              )}
            </>
          )}
        </Col>
      </Row>

      {/* Mobile Filters Drawer */}
      <Drawer
        title="Filter Jobs"
        placement="bottom"
        height="80vh"
        open={filtersVisible}
        onClose={() => setFiltersVisible(false)}
        className="sm:hidden"
      >
        <JobFilters
          onFilter={handleFilterChange}
          initialValues={filters}
          loading={loading}
          isMobile={true}
        />
        {Object.keys(filters).length > 0 && (
          <Button
            block
            onClick={handleClearFilters}
            className="mt-4"
          >
            Clear All Filters
          </Button>
        )}
      </Drawer>

      {/* Job Detail Modal */}
      <JobDetailModal
        job={selectedJob}
        open={jobDetailModalVisible}
        onClose={() => {
          setJobDetailModalVisible(false);
          setSelectedJob(null);
        }}
        onApply={() => {
          setJobDetailModalVisible(false);
          handleApplyClick(selectedJob);
        }}
        onSave={() => handleSaveClick(selectedJob)}
        isApplied={selectedJob ? hasAppliedToJob(selectedJob.id) : false}
        isSaved={selectedJob ? isJobSaved(selectedJob.id) : false}
        loading={loading}
      />

      {/* Job Application Modal */}
      <JobApplicationModal
        job={jobToApply}
        open={applicationModalVisible}
        onClose={() => {
          setApplicationModalVisible(false);
          setJobToApply(null);
        }}
        onSubmit={handleApplicationSubmit}
        loading={applicationLoading}
      />
    </div>
  );
};

export default Jobs;
