# Supabase Query Error Fix ✅

## Issue
The interviewer service was throwing a PostgreSQL error:
```
Error fetching scheduled interviews: 
PGRST100: "failed to parse select parameter (*,candidates:candidate_id(id,full_name,email,mobile_number,years_experience,current_job_title,current_company,profile_photo_url),jobs:job_id(id,title,company_id),companies:jobs.company_id(id,company_name,company_logo_url))" 
(line 1, column 178)
unexpected "m" expecting "count"
```

## Root Causes

### 1. **Incorrect Table References**
- ❌ **Before**: `candidates:candidate_id` (wrong table name)
- ✅ **After**: `candidate_profiles:candidate_id` (correct table name)

### 2. **Non-existent Column**
- ❌ **Before**: `mobile_number` (doesn't exist in candidate_profiles)
- ✅ **After**: Removed this field from the query

### 3. **Incorrect Nested Relationship**
- ❌ **Before**: `companies:jobs.company_id` (invalid syntax)
- ✅ **After**: `company_profiles:company_id` (direct relationship)

### 4. **Missing Email Field**
- ❌ **Before**: Included `email` in candidate_profiles query
- ✅ **After**: Removed since email is in profiles table, not candidate_profiles

## Solutions Applied

### **Fixed Functions:**
1. `getInterviewRequests()` - Line 156
2. `getScheduledInterviews()` - Line 201  
3. `getCompletedInterviews()` - Line 244

### **Corrected Query Structure:**
```javascript
// Before (BROKEN)
.select(`
  *,
  candidates:candidate_id (
    id,
    full_name,
    email,                    // ❌ Not in candidate_profiles
    mobile_number,            // ❌ Doesn't exist
    years_experience,
    current_job_title,
    current_company,
    profile_photo_url
  ),
  jobs:job_id (
    id,
    title,
    company_id
  ),
  companies:jobs.company_id ( // ❌ Invalid nested syntax
    id,
    company_name,
    company_logo_url
  )
`)

// After (WORKING)
.select(`
  *,
  candidate_profiles:candidate_id (  // ✅ Correct table name
    id,
    full_name,
    profile_photo_url,              // ✅ Valid fields only
    years_experience,
    current_job_title,
    current_company
  ),
  jobs:job_id (
    id,
    title,
    company_id
  ),
  company_profiles:company_id (      // ✅ Direct relationship
    id,
    company_name,
    company_logo_url
  )
`)
```

## Database Schema Understanding

### **Correct Table Relationships:**
```
interviews
├── candidate_id → candidate_profiles.id
├── job_id → jobs.id
└── company_id → company_profiles.id

profiles (separate table)
├── id (links to candidate_profiles.id)
├── email
└── phone_number
```

### **Field Locations:**
- **candidate_profiles**: `full_name`, `profile_photo_url`, `years_experience`, `current_job_title`, `current_company`
- **profiles**: `email`, `phone_number`
- **jobs**: `title`, `company_id`
- **company_profiles**: `company_name`, `company_logo_url`

## Files Modified

### `src/features/interviewer/services/interviewer.service.js`
- ✅ Fixed `getInterviewRequests()` function
- ✅ Fixed `getScheduledInterviews()` function  
- ✅ Fixed `getCompletedInterviews()` function
- ✅ All queries now use correct table names and field references

## Verification
- ✅ No diagnostic errors
- ✅ All Supabase queries use valid syntax
- ✅ Table relationships correctly defined
- ✅ Only existing fields referenced

## Impact
- ✅ Interviewer dashboard will now load without errors
- ✅ Interview requests will display properly
- ✅ Scheduled interviews will show candidate information
- ✅ Completed interviews will render correctly

## Best Practices Applied
1. **Correct Table Names**: Always use the actual table names from the database
2. **Valid Field References**: Only reference fields that exist in the target table
3. **Direct Relationships**: Use direct foreign key relationships, not nested paths
4. **Schema Awareness**: Understand which fields are in which tables

The interviewer service should now work correctly without any PostgreSQL parsing errors!
