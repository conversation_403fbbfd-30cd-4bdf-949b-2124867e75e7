/**
 * Interview Service
 *
 * This service handles all API calls related to interviews.
 */

import { supabase } from '@/utils/supabaseClient';

/**
 * Get an interview by ID
 * @param {string} id - Interview ID
 * @returns {Promise<Object>} - Interview data
 */
export const getInterview = async (id) => {
  try {
    const { data, error } = await supabase
      .from('interviews')
      .select(
        `
        *,
        candidates:candidate_id (
          id,
          full_name,
          email,
          phone_number,
          years_experience,
          current_job_title,
          current_company,
          profile_photo_url
        ),
        interviewers:interviewer_id (
          id,
          full_name,
          current_designation,
          current_company
        ),
        jobs:job_id (
          id,
          title,
          company_id
        ),
        companies:jobs.company_id (
          id,
          company_name,
          company_logo_url
        )
      `
      )
      .eq('id', id)
      .single();

    if (error) throw error;
    return { success: true, data };
  } catch (error) {
    console.error('Error fetching interview:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Create a new interview request
 * @param {Object} interviewData - Interview data
 * @returns {Promise<Object>} - Result of the operation
 */
export const createInterviewRequest = async (interviewData) => {
  try {
    // Set the status to 'requested'
    const data = {
      ...interviewData,
      status: 'requested',
      created_at: new Date().toISOString(),
    };

    const { data: result, error } = await supabase.from('interviews').insert(data).select(`
        *,
        jobs:job_id (
          id,
          title
        ),
        company_profiles:company_id (
          company_name,
          company_logo_url
        )
      `);

    if (error) throw error;
    return { success: true, data: result[0] };
  } catch (error) {
    console.error('Error creating interview request:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Update an interview
 * @param {string} id - Interview ID
 * @param {Object} updates - Fields to update
 * @returns {Promise<Object>} - Result of the operation
 */
export const updateInterview = async (id, updates) => {
  try {
    const { data, error } = await supabase.from('interviews').update(updates).eq('id', id).select();

    if (error) throw error;
    return { success: true, data };
  } catch (error) {
    console.error('Error updating interview:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Accept an interview request
 * @param {string} id - Interview ID
 * @param {string} interviewerId - ID of the interviewer accepting the request
 * @param {Object} scheduleData - Optional schedule data
 * @returns {Promise<Object>} - Result of the operation
 */
export const acceptInterviewRequest = async (id, interviewerId, scheduleData = {}) => {
  try {
    const updateData = {
      status: 'scheduled',
      interviewer_id: interviewerId,
      accepted_at: new Date().toISOString(),
    };

    // Add schedule data if provided
    if (scheduleData.interview_date) {
      updateData.interview_date = scheduleData.interview_date;
    }
    if (scheduleData.duration_minutes) {
      updateData.duration_minutes = scheduleData.duration_minutes;
    }
    if (scheduleData.meeting_link) {
      updateData.meeting_link = scheduleData.meeting_link;
    }

    const { data, error } = await supabase
      .from('interviews')
      .update(updateData)
      .eq('id', id)
      .eq('status', 'requested') // Only accept if still in requested status
      .select(`
        *,
        candidates:candidate_id (
          id,
          full_name,
          email,
          profile_photo_url
        ),
        jobs:job_id (
          id,
          title,
          company_id
        ),
        company_profiles:company_id (
          company_name,
          company_logo_url
        )
      `);

    if (error) throw error;

    if (!data || data.length === 0) {
      throw new Error('Interview request not found or already accepted by another interviewer');
    }

    return { success: true, data: data[0] };
  } catch (error) {
    console.error('Error accepting interview request:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Schedule an interview
 * @param {string} id - Interview ID
 * @param {Object} scheduleData - Schedule data (date, time, duration)
 * @returns {Promise<Object>} - Result of the operation
 */
export const scheduleInterview = async (id, scheduleData) => {
  try {
    const { data, error } = await supabase
      .from('interviews')
      .update({
        status: 'scheduled',
        interview_date: scheduleData.date,
        duration_minutes: scheduleData.duration,
        meeting_link: scheduleData.meetingLink,
      })
      .eq('id', id)
      .select();

    if (error) throw error;
    return { success: true, data };
  } catch (error) {
    console.error('Error scheduling interview:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Complete an interview and submit feedback
 * @param {string} id - Interview ID
 * @param {Object} feedbackData - Feedback data (score, feedback)
 * @returns {Promise<Object>} - Result of the operation
 */
export const completeInterview = async (id, feedbackData) => {
  try {
    const { data, error } = await supabase
      .from('interviews')
      .update({
        status: 'completed',
        score: feedbackData.score,
        feedback: feedbackData.feedback,
      })
      .eq('id', id)
      .select();

    if (error) throw error;

    // If there's a job application associated with this interview,
    // update the application status and score
    if (data[0].job_id) {
      const { data: applicationData, error: applicationError } = await supabase
        .from('applications')
        .select('*')
        .eq('candidate_id', data[0].candidate_id)
        .eq('job_id', data[0].job_id)
        .single();

      if (!applicationError && applicationData) {
        await supabase
          .from('applications')
          .update({
            status: 'interviewed',
            interview_score: feedbackData.score,
          })
          .eq('id', applicationData.id);
      }
    }

    return { success: true, data };
  } catch (error) {
    console.error('Error completing interview:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Cancel an interview
 * @param {string} id - Interview ID
 * @param {string} reason - Reason for cancellation
 * @returns {Promise<Object>} - Result of the operation
 */
export const cancelInterview = async (id, reason) => {
  try {
    const { data, error } = await supabase
      .from('interviews')
      .update({
        status: 'cancelled',
        cancellation_reason: reason,
      })
      .eq('id', id)
      .select();

    if (error) throw error;
    return { success: true, data };
  } catch (error) {
    console.error('Error cancelling interview:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get all pending interview requests for interviewers
 * @returns {Promise<Object>} - All pending interview requests
 */
export const getAllInterviewRequests = async () => {
  try {
    const { data, error } = await supabase
      .from('interviews')
      .select(
        `
        *,
        candidates:candidate_id (
          id,
          full_name,
          email,
          profile_photo_url,
          current_job_title,
          years_experience
        ),
        jobs:job_id (
          id,
          title,
          experience_level,
          required_skills
        ),
        company_profiles:company_id (
          company_name,
          company_logo_url
        )
      `
      )
      .eq('status', 'requested')
      .is('interviewer_id', null)
      .order('created_at', { ascending: false });

    if (error) throw error;

    return { success: true, data: data || [] };
  } catch (error) {
    console.error('Error fetching interview requests:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Decline an interview request
 * @param {string} id - Interview ID
 * @param {string} reason - Decline reason (optional)
 * @returns {Promise<Object>} - Result of the operation
 */
export const declineInterviewRequest = async (id, reason = '') => {
  try {
    const { data, error } = await supabase
      .from('interviews')
      .update({
        status: 'declined',
        decline_reason: reason,
        declined_at: new Date().toISOString(),
      })
      .eq('id', id)
      .eq('status', 'requested')
      .select();

    if (error) throw error;

    if (!data || data.length === 0) {
      throw new Error('Interview request not found or already processed');
    }

    return { success: true, data: data[0] };
  } catch (error) {
    console.error('Error declining interview request:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Match a candidate with an interviewer
 * @param {string} _candidateId - Candidate ID (unused but kept for API compatibility)
 * @param {string} role - Role applied for
 * @returns {Promise<Object>} - Matched interviewer
 */
export const matchCandidateWithInterviewer = async (_candidateId, role) => {
  try {
    // Find interviewers who can interview for this role
    const { data, error } = await supabase
      .from('interviewer_profiles')
      .select('*')
      .contains('preferred_interview_roles', [{ role }]);

    if (error) throw error;

    if (data.length === 0) {
      return { success: false, error: 'No matching interviewers found' };
    }

    // For now, just return the first matching interviewer
    // In a real system, you'd implement a more sophisticated matching algorithm
    return { success: true, data: data[0] };
  } catch (error) {
    console.error('Error matching candidate with interviewer:', error);
    return { success: false, error: error.message };
  }
};
