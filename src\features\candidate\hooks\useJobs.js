/**
 * use<PERSON>obs Hook for Candidates
 *
 * Handles fetching and managing jobs data for candidates.
 * Integrates with candidate store for state management.
 * Uses optimized caching for better performance.
 */

import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/utils/supabaseClient';
import useAuth from '@/hooks/useAuth';
import useCandidateStore from '@/features/candidate/store/candidate.store';
import dataFetchService from '@/services/dataFetchService';

const useJobs = () => {
  const { user, profile } = useAuth();
  const [jobs, setJobs] = useState([]);
  const [filteredJobs, setFilteredJobs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({});
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 12,
    total: 0,
  });

  // Get applied and saved jobs from store
  const { appliedJobs, savedJobs } = useCandidateStore();

  /**
   * Fetch all available jobs with optional filters
   * Uses optimized caching for better performance
   */
  const fetchJobs = useCallback(
    async (filterParams = {}, page = 1, pageSize = 12, forceRefresh = false) => {
      setLoading(true);
      setError(null);

      try {
        // Prepare filter params for data service
        const validFilters = {};
        Object.entries(filterParams)
          .filter(([_, value]) => value !== undefined && value !== null)
          .forEach(([key, value]) => {
            validFilters[key] = value;
          });

        // Try to get recommended jobs from cache first
        if (!forceRefresh && user?.id) {
          const cachedJobs = await dataFetchService.fetchRecommendedJobs(
            user.id,
            validFilters,
            false
          );

          if (cachedJobs && cachedJobs.length > 0) {
            // Apply pagination to cached results
            const from = (page - 1) * pageSize;
            const to = Math.min(from + pageSize, cachedJobs.length);
            const paginatedJobs = cachedJobs.slice(from, to);

            // Process jobs to add computed fields
            const processedJobs = paginatedJobs.map((job) => ({
              ...job,
              matchPercentage: calculateJobMatch(job, profile),
              isApplied: appliedJobs.some((app) => app.job_id === job.id),
              isSaved: savedJobs.some((saved) => saved.job_id === job.id),
            }));

            setJobs(cachedJobs);
            setFilteredJobs(processedJobs);
            setPagination({
              ...pagination,
              current: page,
              pageSize,
              total: cachedJobs.length,
            });
            setFilters(filterParams);

            setLoading(false);
            return { data: processedJobs, totalCount: cachedJobs.length };
          }
        }

        // If not in cache or forced refresh, fetch from database
        let query = supabase
          .from('jobs')
          .select(
            `
          *,
          companies:company_id (
            id,
            company_name,
            company_logo_url,
            company_type,
            company_size,
            office_locations,
            website_url
          )
        `
          )
          .eq('status', 'active')
          .order('created_at', { ascending: false });

        // Apply filters
        if (validFilters.location) {
          query = query.ilike('location', `%${validFilters.location}%`);
        }

        if (validFilters.experienceLevel) {
          query = query.eq('experience_level', validFilters.experienceLevel);
        }

        if (validFilters.jobType) {
          query = query.eq('job_type', validFilters.jobType);
        }

        if (validFilters.keyword) {
          query = query.or(
            `title.ilike.%${validFilters.keyword}%,description.ilike.%${validFilters.keyword}%`
          );
        }

        // Calculate pagination
        const from = (page - 1) * pageSize;
        const to = from + pageSize - 1;

        // Apply pagination
        query = query.range(from, to);

        // Execute query
        const { data, error } = await query.limit(pageSize);

        if (error) throw error;

        // Get total count for pagination
        const { count: totalCount, error: countError } = await supabase
          .from('jobs')
          .select('id', { count: 'exact' })
          .eq('status', 'active');

        if (countError) throw countError;

        // Process jobs to add computed fields
        const processedJobs = (data || []).map((job) => ({
          ...job,
          matchPercentage: calculateJobMatch(job, profile),
          isApplied: appliedJobs.some((app) => app.job_id === job.id),
          isSaved: savedJobs.some((saved) => saved.job_id === job.id),
        }));

        // Update state
        setJobs(data || []);
        setFilteredJobs(processedJobs);
        setPagination({
          ...pagination,
          current: page,
          pageSize,
          total: totalCount,
        });
        setFilters(filterParams);

        // Cache the results if user is logged in
        if (user?.id) {
          // Store the full result set in cache for future use
          await dataFetchService.fetchRecommendedJobs(user.id, validFilters, true);
        }

        return { data: processedJobs, totalCount };
      } catch (error) {
        console.error('Error fetching jobs:', error);
        setError(error.message);
        return { data: [], totalCount: 0 };
      } finally {
        setLoading(false);
      }
    },
    [pagination, user, profile, appliedJobs, savedJobs]
  );

  /**
   * Calculate job match percentage based on candidate profile
   */
  const calculateJobMatch = useCallback((job, candidateProfile) => {
    if (!job || !candidateProfile) return 0;

    // Simple match algorithm based on skills, experience, and location
    let matchScore = 0;
    let totalFactors = 0;

    // Match based on skills (if both have skills data)
    if (job.required_skills && candidateProfile.skills) {
      const jobSkills = Array.isArray(job.required_skills)
        ? job.required_skills
        : typeof job.required_skills === 'string'
          ? JSON.parse(job.required_skills)
          : [];

      const candidateSkills = Array.isArray(candidateProfile.skills)
        ? candidateProfile.skills
        : typeof candidateProfile.skills === 'string'
          ? JSON.parse(candidateProfile.skills)
          : [];

      if (jobSkills.length > 0 && candidateSkills.length > 0) {
        const matchingSkills = jobSkills.filter((skill) =>
          candidateSkills.some((candidateSkill) =>
            candidateSkill.toLowerCase().includes(skill.toLowerCase())
          )
        );

        matchScore += (matchingSkills.length / jobSkills.length) * 100;
        totalFactors++;
      }
    }

    // Match based on experience level
    if (job.experience_level && candidateProfile.years_experience) {
      const experienceLevels = {
        entry: { min: 0, max: 2 },
        mid: { min: 2, max: 5 },
        senior: { min: 5, max: 10 },
        executive: { min: 10, max: 100 },
      };

      const jobExperience = experienceLevels[job.experience_level.toLowerCase()];
      if (jobExperience) {
        const candidateYears = parseInt(candidateProfile.years_experience);
        if (
          !isNaN(candidateYears) &&
          candidateYears >= jobExperience.min &&
          candidateYears <= jobExperience.max
        ) {
          matchScore += 100;
        } else if (!isNaN(candidateYears) && candidateYears > jobExperience.max) {
          matchScore += 75; // Overqualified
        } else if (!isNaN(candidateYears) && candidateYears < jobExperience.min) {
          matchScore += 25; // Underqualified
        }
        totalFactors++;
      }
    }

    // Match based on location
    if (job.location && candidateProfile.location) {
      if (
        job.location.toLowerCase().includes(candidateProfile.location.toLowerCase()) ||
        candidateProfile.location.toLowerCase().includes(job.location.toLowerCase())
      ) {
        matchScore += 100;
        totalFactors++;
      } else if (job.location.toLowerCase().includes('remote')) {
        matchScore += 100;
        totalFactors++;
      }
    }

    // Calculate average match percentage
    const matchPercentage = totalFactors > 0 ? Math.round(matchScore / totalFactors) : 0;

    return Math.min(matchPercentage, 100); // Cap at 100%
  }, []);

  /**
   * Get job by ID
   * Uses optimized caching for better performance
   */
  const getJobById = useCallback(
    async (jobId, forceRefresh = false) => {
      if (!jobId) return null;

      setLoading(true);
      setError(null);

      try {
        // Try to get from cache first if user is logged in
        if (!forceRefresh && user?.id) {
          // Check if the job is in the cached recommended jobs
          const cachedJobs = await dataFetchService.fetchRecommendedJobs(user.id, {}, false);
          if (cachedJobs) {
            const cachedJob = cachedJobs.find((job) => job.id === jobId);
            if (cachedJob) {
              // Process job data to add computed fields
              const processedJob = {
                ...cachedJob,
                matchPercentage: calculateJobMatch(cachedJob, profile),
                isApplied: appliedJobs.some((app) => app.job_id === cachedJob.id),
                isSaved: savedJobs.some((saved) => saved.job_id === cachedJob.id),
              };
              setLoading(false);
              return processedJob;
            }
          }
        }

        // If not in cache or forced refresh, fetch from database
        const { data, error: fetchError } = await supabase
          .from('jobs')
          .select(
            `
            *,
            companies:company_id (
              id,
              company_name,
              company_logo_url,
              company_type,
              company_size,
              office_locations,
              website_url
            )
          `
          )
          .eq('id', jobId)
          .single();

        if (fetchError) throw fetchError;

        // Process job data to add computed fields
        const processedJob = {
          ...data,
          matchPercentage: calculateJobMatch(data, profile),
          isApplied: appliedJobs.some((app) => app.job_id === data.id),
          isSaved: savedJobs.some((saved) => saved.job_id === data.id),
        };

        return processedJob;
      } catch (error) {
        console.error('Error fetching job:', error);
        setError(error.message);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [profile, appliedJobs, savedJobs, calculateJobMatch, user]
  );

  // Initial fetch on mount
  useEffect(() => {
    if (user) {
      fetchJobs();
    }
  }, [user, fetchJobs]);

  return {
    // Data
    jobs,
    filteredJobs,
    pagination,
    filters,

    // UI states
    loading,
    error,

    // Actions
    fetchJobs,
    getJobById,

    // Utilities
    calculateJobMatch,
    refetch: () => fetchJobs(filters, pagination.current, pagination.pageSize),
    clearFilters: () => {
      setFilters({});
      fetchJobs({}, 1, pagination.pageSize);
    },
  };
};

export default useJobs;
