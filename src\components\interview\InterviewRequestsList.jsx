/**
 * InterviewRequestsList Component
 * 
 * Component for interviewers to view and manage interview requests from candidates
 * Shows all pending requests and allows accept/decline actions
 */

import { useState, useEffect } from 'react';
import {
  Card,
  List,
  Avatar,
  Button,
  Tag,
  Space,
  Typography,
  Empty,
  Skeleton,
  Modal,
  Form,
  DatePicker,
  TimePicker,
  Input,
  message,
  Tooltip,
  Badge,
} from 'antd';
import {
  UserOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  CheckOutlined,
  CloseOutlined,
  BriefcaseOutlined,
  MessageOutlined,
  VideoCameraOutlined,
} from '@ant-design/icons';
import { getAllInterviewRequests, acceptInterviewRequest, declineInterviewRequest } from '@/services/interview.service';
import useAuth from '@/hooks/useAuth';
import dayjs from 'dayjs';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

const InterviewRequestsList = () => {
  const { user } = useAuth();
  const [requests, setRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [acceptModalVisible, setAcceptModalVisible] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [acceptForm] = Form.useForm();
  const [actionLoading, setActionLoading] = useState(false);

  // Load interview requests
  useEffect(() => {
    loadInterviewRequests();
  }, []);

  const loadInterviewRequests = async () => {
    setLoading(true);
    try {
      const result = await getAllInterviewRequests();
      if (result.success) {
        setRequests(result.data);
      } else {
        message.error('Failed to load interview requests');
      }
    } catch (error) {
      console.error('Error loading interview requests:', error);
      message.error('Failed to load interview requests');
    } finally {
      setLoading(false);
    }
  };

  // Handle accept interview request
  const handleAcceptRequest = async (request) => {
    setSelectedRequest(request);
    setAcceptModalVisible(true);
    
    // Pre-fill form with request data
    acceptForm.setFieldsValue({
      interview_date: dayjs(request.preferred_date),
      interview_time: dayjs(request.preferred_date),
      duration_minutes: request.duration_minutes || 60,
      meeting_link: '', // Will be generated or provided
    });
  };

  // Submit accept form
  const handleAcceptSubmit = async (values) => {
    try {
      setActionLoading(true);

      // Combine date and time
      const interviewDateTime = values.interview_date
        .hour(values.interview_time.hour())
        .minute(values.interview_time.minute());

      const scheduleData = {
        interview_date: interviewDateTime.toISOString(),
        duration_minutes: values.duration_minutes,
        meeting_link: values.meeting_link,
      };

      const result = await acceptInterviewRequest(selectedRequest.id, user.id, scheduleData);

      if (result.success) {
        message.success('Interview request accepted successfully!');
        setAcceptModalVisible(false);
        acceptForm.resetFields();
        setSelectedRequest(null);
        
        // Remove the accepted request from the list
        setRequests(prev => prev.filter(req => req.id !== selectedRequest.id));
      } else {
        message.error(result.error || 'Failed to accept interview request');
      }
    } catch (error) {
      console.error('Error accepting interview request:', error);
      message.error('Failed to accept interview request');
    } finally {
      setActionLoading(false);
    }
  };

  // Handle decline interview request
  const handleDeclineRequest = async (request) => {
    Modal.confirm({
      title: 'Decline Interview Request',
      content: 'Are you sure you want to decline this interview request?',
      okText: 'Decline',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: async () => {
        try {
          const result = await declineInterviewRequest(request.id, 'Declined by interviewer');
          
          if (result.success) {
            message.success('Interview request declined');
            // Remove the declined request from the list
            setRequests(prev => prev.filter(req => req.id !== request.id));
          } else {
            message.error(result.error || 'Failed to decline interview request');
          }
        } catch (error) {
          console.error('Error declining interview request:', error);
          message.error('Failed to decline interview request');
        }
      },
    });
  };

  // Format date for display
  const formatDate = (dateString) => {
    return dayjs(dateString).format('MMM DD, YYYY [at] HH:mm');
  };

  // Get experience badge color
  const getExperienceBadgeColor = (years) => {
    if (years <= 2) return 'green';
    if (years <= 5) return 'blue';
    if (years <= 10) return 'orange';
    return 'red';
  };

  if (loading) {
    return (
      <Card>
        <Skeleton active paragraph={{ rows: 5 }} />
      </Card>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <Title level={3} className="mb-0">
          Interview Requests
        </Title>
        <Badge count={requests.length} showZero>
          <Button onClick={loadInterviewRequests}>
            Refresh
          </Button>
        </Badge>
      </div>

      <Card>
        {requests.length > 0 ? (
          <List
            itemLayout="vertical"
            dataSource={requests}
            renderItem={(request) => (
              <List.Item
                key={request.id}
                actions={[
                  <Button
                    type="primary"
                    icon={<CheckOutlined />}
                    onClick={() => handleAcceptRequest(request)}
                  >
                    Accept
                  </Button>,
                  <Button
                    danger
                    icon={<CloseOutlined />}
                    onClick={() => handleDeclineRequest(request)}
                  >
                    Decline
                  </Button>,
                ]}
              >
                <List.Item.Meta
                  avatar={
                    <Avatar
                      size={64}
                      src={request.candidates?.profile_photo_url}
                      icon={!request.candidates?.profile_photo_url && <UserOutlined />}
                    />
                  }
                  title={
                    <Space direction="vertical" size={0}>
                      <Text strong className="text-lg">
                        {request.candidates?.full_name}
                      </Text>
                      <Text type="secondary">
                        {request.candidates?.current_job_title}
                      </Text>
                    </Space>
                  }
                  description={
                    <Space direction="vertical" size={4} className="mt-2">
                      <Space>
                        <BriefcaseOutlined />
                        <Text strong>{request.jobs?.title}</Text>
                        <Text type="secondary">at {request.company_profiles?.company_name}</Text>
                      </Space>
                      <Space>
                        <CalendarOutlined />
                        <Text>Preferred: {formatDate(request.preferred_date)}</Text>
                      </Space>
                      <Space>
                        <ClockCircleOutlined />
                        <Text>Duration: {request.duration_minutes || 60} minutes</Text>
                      </Space>
                      <Space>
                        <VideoCameraOutlined />
                        <Text>Type: {request.interview_type || 'video'}</Text>
                      </Space>
                      {request.candidates?.years_experience && (
                        <Space>
                          <Tag color={getExperienceBadgeColor(request.candidates.years_experience)}>
                            {request.candidates.years_experience} years experience
                          </Tag>
                        </Space>
                      )}
                      {request.message && (
                        <div className="mt-2">
                          <Text type="secondary">
                            <MessageOutlined /> Message:
                          </Text>
                          <Paragraph className="mt-1 ml-4 text-sm">
                            "{request.message}"
                          </Paragraph>
                        </div>
                      )}
                    </Space>
                  }
                />
              </List.Item>
            )}
            pagination={{
              pageSize: 5,
              showSizeChanger: true,
              showQuickJumper: true,
            }}
          />
        ) : (
          <Empty
            description="No interview requests at the moment"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        )}
      </Card>

      {/* Accept Interview Modal */}
      <Modal
        title="Accept Interview Request"
        open={acceptModalVisible}
        onCancel={() => {
          setAcceptModalVisible(false);
          acceptForm.resetFields();
          setSelectedRequest(null);
        }}
        footer={null}
        width={600}
      >
        {selectedRequest && (
          <div>
            <div className="mb-4 p-4 bg-gray-50 rounded">
              <Text strong>Candidate: </Text>
              <Text>{selectedRequest.candidates?.full_name}</Text>
              <br />
              <Text strong>Position: </Text>
              <Text>{selectedRequest.jobs?.title}</Text>
              <br />
              <Text strong>Requested Time: </Text>
              <Text>{formatDate(selectedRequest.preferred_date)}</Text>
            </div>

            <Form
              form={acceptForm}
              layout="vertical"
              onFinish={handleAcceptSubmit}
            >
              <div className="grid grid-cols-2 gap-4">
                <Form.Item
                  name="interview_date"
                  label="Interview Date"
                  rules={[{ required: true, message: 'Please select interview date' }]}
                >
                  <DatePicker
                    className="w-full"
                    disabledDate={(current) => current && current < dayjs().startOf('day')}
                  />
                </Form.Item>

                <Form.Item
                  name="interview_time"
                  label="Interview Time"
                  rules={[{ required: true, message: 'Please select interview time' }]}
                >
                  <TimePicker
                    className="w-full"
                    format="HH:mm"
                    minuteStep={15}
                  />
                </Form.Item>
              </div>

              <Form.Item
                name="duration_minutes"
                label="Duration (minutes)"
                rules={[{ required: true, message: 'Please enter duration' }]}
              >
                <Input type="number" min={15} max={180} />
              </Form.Item>

              <Form.Item
                name="meeting_link"
                label="Meeting Link (Optional)"
                extra="Leave empty to generate a video call invitation later"
              >
                <Input placeholder="https://meet.google.com/..." />
              </Form.Item>

              <Form.Item className="mb-0">
                <Space className="w-full justify-end">
                  <Button onClick={() => setAcceptModalVisible(false)}>
                    Cancel
                  </Button>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={actionLoading}
                    icon={<CheckOutlined />}
                  >
                    Accept Interview
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default InterviewRequestsList;
