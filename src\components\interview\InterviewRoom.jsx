/**
 * InterviewRoom Component
 * 
 * Global video call interview room that can be used by all roles
 * Supports video calling, screen sharing, and real-time chat
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Typography,
  Avatar,
  Badge,
  Tooltip,
  Drawer,
  Input,
  List,
  Spin,
  Alert,
  Row,
  Col,
  Divider,
} from 'antd';
import {
  VideoCameraOutlined,
  AudioOutlined,
  AudioMutedOutlined,
  DesktopOutlined,
  MessageOutlined,
  PhoneOutlined,
  SettingOutlined,
  FullscreenOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import useVideoCall from '@/hooks/useVideoCall';
import useAuth from '@/hooks/useAuth';
import { getInvitationByToken, acceptInvitation } from '@/services/interviewInvitation.service';
import { Building } from 'lucide-react';

const { Title, Text } = Typography;
const { TextArea } = Input;

const InterviewRoom = () => {
  const { token } = useParams();
  const navigate = useNavigate();
  const { user, profile } = useAuth();

  // Interview data
  const [invitation, setInvitation] = useState(null);
  const [interview, setInterview] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [hasAccepted, setHasAccepted] = useState(false);

  // Chat states
  const [newMessage, setNewMessage] = useState('');

  // Video call hook
  const {
    localVideoRef,
    remoteVideoRef,
    isCallActive,
    isConnecting,
    isConnected,
    isMuted,
    isVideoOff,
    isScreenSharing,
    connectionState,
    chatMessages,
    unreadCount,
    isChatOpen,
    callDuration,
    formattedDuration,
    initializeCall,
    startCall,
    endCall,
    toggleAudio,
    toggleVideo,
    startScreenShare,
    stopScreenShare,
    sendChatMessage,
    toggleChat,
  } = useVideoCall(interview?.id, user?.id, profile?.role);

  // Load invitation data
  useEffect(() => {
    const loadInvitation = async () => {
      if (!token) {
        setError('Invalid invitation link');
        setLoading(false);
        return;
      }

      try {
        const result = await getInvitationByToken(token);
        if (result.success) {
          setInvitation(result.invitation);
          setInterview(result.invitation.interviews);
        } else {
          setError(result.error);
        }
      } catch (err) {
        setError('Failed to load invitation');
      } finally {
        setLoading(false);
      }
    };

    loadInvitation();
  }, [token]);

  // Handle invitation acceptance
  const handleAcceptInvitation = async () => {
    try {
      const result = await acceptInvitation(token, user?.id);
      if (result.success) {
        setHasAccepted(true);
        await initializeCall();
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError('Failed to accept invitation');
    }
  };

  // Handle call start
  const handleStartCall = async () => {
    if (!isCallActive) {
      await initializeCall();
    }
    await startCall();
  };

  // Handle call end
  const handleEndCall = () => {
    endCall();
    navigate('/');
  };

  // Handle send message
  const handleSendMessage = () => {
    if (newMessage.trim()) {
      sendChatMessage(newMessage.trim());
      setNewMessage('');
    }
  };

  // Get participant info
  const getParticipantInfo = () => {
    if (!interview) return null;

    const participants = [];
    
    if (interview.candidate_profiles) {
      participants.push({
        role: 'candidate',
        name: interview.candidate_profiles.full_name,
        avatar: interview.candidate_profiles.profile_photo_url,
        title: interview.candidate_profiles.current_job_title,
      });
    }

    if (interview.interviewer_profiles) {
      participants.push({
        role: 'interviewer',
        name: interview.interviewer_profiles.full_name,
        avatar: interview.interviewer_profiles.profile_photo_url,
        title: interview.interviewer_profiles.current_designation,
      });
    }

    return participants;
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Spin size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Alert
          message="Error"
          description={error}
          type="error"
          showIcon
          action={
            <Button onClick={() => navigate('/')}>
              Go Home
            </Button>
          }
        />
      </div>
    );
  }

  const participants = getParticipantInfo();

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Avatar
                src={interview?.company_profiles?.company_logo_url}
                icon={!interview?.company_profiles?.company_logo_url && <Building size={20} />}
                size={40}
              />
              <div>
                <Title level={4} className="mb-0">
                  {interview?.jobs?.title}
                </Title>
                <Text type="secondary">
                  {interview?.company_profiles?.company_name}
                </Text>
              </div>
            </Space>
          </Col>
          <Col>
            <Space>
              {isConnected && (
                <Badge status="processing" text={`Connected • ${formattedDuration}`} />
              )}
              <Button
                danger
                icon={<PhoneOutlined />}
                onClick={handleEndCall}
                disabled={!isCallActive}
              >
                End Call
              </Button>
            </Space>
          </Col>
        </Row>
      </div>

      {/* Main Content */}
      <div className="flex h-[calc(100vh-80px)]">
        {/* Video Area */}
        <div className="flex-1 relative bg-black">
          {/* Remote Video */}
          <video
            ref={remoteVideoRef}
            autoPlay
            playsInline
            className="w-full h-full object-cover"
          />

          {/* Local Video (Picture-in-Picture) */}
          <div className="absolute top-4 right-4 w-64 h-48 bg-gray-800 rounded-lg overflow-hidden">
            <video
              ref={localVideoRef}
              autoPlay
              playsInline
              muted
              className="w-full h-full object-cover"
            />
          </div>

          {/* Connection Status */}
          {isConnecting && (
            <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
              <div className="text-center text-white">
                <Spin size="large" />
                <div className="mt-4">Connecting...</div>
              </div>
            </div>
          )}

          {/* Controls */}
          <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2">
            <Space size="large">
              <Tooltip title={isMuted ? 'Unmute' : 'Mute'}>
                <Button
                  type={isMuted ? 'primary' : 'default'}
                  danger={isMuted}
                  shape="circle"
                  size="large"
                  icon={isMuted ? <AudioMutedOutlined /> : <AudioOutlined />}
                  onClick={toggleAudio}
                />
              </Tooltip>

              <Tooltip title={isVideoOff ? 'Turn on camera' : 'Turn off camera'}>
                <Button
                  type={isVideoOff ? 'primary' : 'default'}
                  danger={isVideoOff}
                  shape="circle"
                  size="large"
                  icon={<VideoCameraOutlined />}
                  onClick={toggleVideo}
                />
              </Tooltip>

              <Tooltip title={isScreenSharing ? 'Stop sharing' : 'Share screen'}>
                <Button
                  type={isScreenSharing ? 'primary' : 'default'}
                  shape="circle"
                  size="large"
                  icon={<DesktopOutlined />}
                  onClick={isScreenSharing ? stopScreenShare : startScreenShare}
                />
              </Tooltip>

              <Tooltip title="Chat">
                <Badge count={unreadCount}>
                  <Button
                    type={isChatOpen ? 'primary' : 'default'}
                    shape="circle"
                    size="large"
                    icon={<MessageOutlined />}
                    onClick={toggleChat}
                  />
                </Badge>
              </Tooltip>
            </Space>
          </div>
        </div>

        {/* Sidebar */}
        <div className="w-80 bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 flex flex-col">
          {/* Participants */}
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <Title level={5}>Participants</Title>
            <Space direction="vertical" className="w-full">
              {participants?.map((participant, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <Avatar
                    src={participant.avatar}
                    icon={!participant.avatar && <UserOutlined />}
                  />
                  <div>
                    <div className="font-medium">{participant.name}</div>
                    <div className="text-sm text-gray-500">{participant.title}</div>
                  </div>
                </div>
              ))}
            </Space>
          </div>

          {/* Interview Actions */}
          {!hasAccepted && !isCallActive && (
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <Button
                type="primary"
                block
                size="large"
                onClick={handleAcceptInvitation}
                loading={isConnecting}
              >
                Join Interview
              </Button>
            </div>
          )}

          {hasAccepted && !isConnected && (
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <Button
                type="primary"
                block
                size="large"
                onClick={handleStartCall}
                loading={isConnecting}
              >
                Start Call
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Chat Drawer */}
      <Drawer
        title="Chat"
        placement="right"
        open={isChatOpen}
        onClose={toggleChat}
        width={400}
      >
        <div className="flex flex-col h-full">
          <div className="flex-1 overflow-y-auto mb-4">
            <List
              dataSource={chatMessages}
              renderItem={(message) => (
                <List.Item className={message.sender === 'local' ? 'text-right' : 'text-left'}>
                  <div
                    className={`inline-block p-2 rounded-lg max-w-xs ${
                      message.sender === 'local'
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-200 text-gray-800'
                    }`}
                  >
                    {message.message}
                  </div>
                </List.Item>
              )}
            />
          </div>
          <div>
            <Input.Group compact>
              <Input
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                onPressEnter={handleSendMessage}
                placeholder="Type a message..."
                style={{ width: 'calc(100% - 80px)' }}
              />
              <Button type="primary" onClick={handleSendMessage}>
                Send
              </Button>
            </Input.Group>
          </div>
        </div>
      </Drawer>
    </div>
  );
};

export default InterviewRoom;
