/**
 * LoadingWrapper Component
 * 
 * A smart loading wrapper that prevents infinite loading states
 * and provides better UX for data fetching operations
 */

import { useEffect, useState } from 'react';
import { Skeleton, Spin, Alert } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';

const LoadingWrapper = ({
  loading = false,
  error = null,
  data = null,
  children,
  skeleton = true,
  skeletonProps = {},
  timeout = 10000, // 10 seconds timeout
  fallback = null,
  showError = true,
  retryAction = null,
}) => {
  const [isTimeout, setIsTimeout] = useState(false);
  const [timeoutId, setTimeoutId] = useState(null);

  // Handle loading timeout
  useEffect(() => {
    if (loading && timeout > 0) {
      const id = setTimeout(() => {
        setIsTimeout(true);
      }, timeout);
      setTimeoutId(id);

      return () => {
        if (id) clearTimeout(id);
      };
    } else {
      setIsTimeout(false);
      if (timeoutId) {
        clearTimeout(timeoutId);
        setTimeoutId(null);
      }
    }
  }, [loading, timeout, timeoutId]);

  // Reset timeout when loading stops
  useEffect(() => {
    if (!loading) {
      setIsTimeout(false);
    }
  }, [loading]);

  // Show timeout error
  if (isTimeout) {
    return (
      <Alert
        message="Loading Timeout"
        description="The request is taking longer than expected. Please try again."
        type="warning"
        showIcon
        action={
          retryAction && (
            <button
              onClick={() => {
                setIsTimeout(false);
                retryAction();
              }}
              className="ant-btn ant-btn-sm ant-btn-outline"
            >
              Retry
            </button>
          )
        }
      />
    );
  }

  // Show error state
  if (error && showError) {
    return (
      <Alert
        message="Error Loading Data"
        description={typeof error === 'string' ? error : 'An unexpected error occurred'}
        type="error"
        showIcon
        action={
          retryAction && (
            <button
              onClick={retryAction}
              className="ant-btn ant-btn-sm ant-btn-outline"
            >
              Retry
            </button>
          )
        }
      />
    );
  }

  // Show loading state
  if (loading) {
    if (skeleton) {
      return (
        <Skeleton
          active
          paragraph={{ rows: 4 }}
          {...skeletonProps}
        />
      );
    }

    if (fallback) {
      return fallback;
    }

    return (
      <div className="flex justify-center items-center py-8">
        <Spin
          indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />}
          tip="Loading..."
        />
      </div>
    );
  }

  // Show children when data is loaded
  return children;
};

export default LoadingWrapper;
